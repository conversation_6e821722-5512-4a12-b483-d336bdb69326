path: game
train: images
val: val
test:
tracker_type: bytetrack # tracker type, ['botsort', 'bytetrack']  change this to select tracker
track_high_thresh: 0.25 # threshold for the first association
track_low_thresh: 0.1 # threshold for the second association
new_track_thresh: 0.25 # threshold for init new track if the detection does not match any tracks
track_buffer: 30 # buffer to calculate the time when to remove tracks
match_thresh: 0.8 # threshold for matching tracks
fuse_score: True # Whether to fuse confidence scores with the iou distances before matching
# min_box_area: 10  # threshold for min box areas(for tracker evaluation, not used for now)

# BoT-SORT settings
gmc_method: sparseOptFlow # method of global motion compensation
# ReID model related thresh (not supported yet)
proximity_thresh: 0.5
appearance_thresh: 0.25
with_reid: False

names:
  0: player
  1: bot
  2: weapon
  3: outline
  4: dead_body
  5: hideout_target_human
  6: hideout_target_balls
  7: head
  8: smoke
  9: fire
  10: third_person