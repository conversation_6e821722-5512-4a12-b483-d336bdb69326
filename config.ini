[Detection window]
detection_window_width = 320
detection_window_height = 320
circle_capture = True

[Capture Methods]
capture_fps = 60
Bettercam_capture = False
bettercam_monitor_id = 0
bettercam_gpu_id = 0
Obs_capture = False
Obs_camera_id = 1
mss_capture = True

[Aim]
body_y_offset = 0.1
hideout_targets = True
disable_headshot = False
disable_prediction = False
prediction_interval = 2.0
third_person = True

[Hotkeys]
hotkey_targeting = RightMouseButton
hotkey_exit = F2
hotkey_pause = F3
hotkey_reload_config = F4

[Mouse]
mouse_dpi = 1100
mouse_sensitivity = 3.0
mouse_fov_width = 40
mouse_fov_height = 40
mouse_min_speed_multiplier = 1.0
mouse_max_speed_multiplier = 1.5
mouse_lock_target = False
mouse_auto_aim = False
mouse_ghub = False
mouse_rzr = False

[Shooting]
auto_shoot = False
triggerbot = False
force_click = False
bScope_multiplier = 1.0

[Arduino]
arduino_move = False
arduino_shoot = False
arduino_port = auto
arduino_baudrate = 9600
arduino_16_bit_mouse = False

[AI]
# You can find new improved models here https://boosty.to/sunone and here https://www.patreon.com/sunone
AI_model_name = sunxds_0.5.6.pt
AI_model_image_size = 640
AI_conf = 0.2
AI_device = 0
AI_enable_AMD = False
disable_tracker = False

[overlay]
show_overlay = False
overlay_show_borders = True
overlay_show_boxes = False
overlay_show_target_line = False
overlay_show_target_prediction_line = False
overlay_show_labels = False
overlay_show_conf = False

[Debug window]
show_window = False
show_detection_speed = True
show_window_fps = False
show_boxes = True
show_labels = False
show_conf = True
show_target_line = False
show_target_prediction_line = False
show_bScope_box = False
show_history_points = False
debug_window_always_on_top = True
spawn_window_pos_x = 100
spawn_window_pos_y = 100
debug_window_scale_percent = 100
debug_window_screenshot_key = End